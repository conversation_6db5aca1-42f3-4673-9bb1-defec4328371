@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Form input visibility fixes */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="date"],
input[type="file"],
select,
textarea {
  background-color: white !important;
  border: 1px solid #d1d5db !important;
  color: #111827 !important;
}



input[type="text"]:focus,
input[type="email"]:focus,
input[type="tel"]:focus,
input[type="date"]:focus,
input[type="file"]:focus,
select:focus,
textarea:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}



/* Ensure form labels are visible */
label {
  color: #374151 !important;
  font-weight: 500 !important;
}



/* Form container styling */
.form-container {
  background: white !important;
  color: #111827 !important;
}



/* Specific input field styling for better visibility */
.w-full.px-4.py-3.border {
  background-color: white !important;
  border: 1px solid #d1d5db !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1rem !important;
  color: #111827 !important;
}

/* Placeholder text styling */
input::placeholder,
textarea::placeholder {
  color: #9ca3af !important;
  opacity: 1 !important;
}

/* Disabled input styling */
input:disabled,
select:disabled,
textarea:disabled {
  background-color: #f3f4f6 !important;
  color: #6b7280 !important;
  cursor: not-allowed !important;
}

/* Ensure dropdown options are visible */
select {
  position: relative !important;
  z-index: 1000 !important;
}

/* Fix for dropdown options being clipped */
.relative {
  overflow: visible !important;
}

/* Ensure dropdown portals are properly styled */
body > div[style*="position: fixed"] {
  z-index: 9999 !important;
}

/* Table container overflow fix for dropdowns */
.overflow-x-auto {
  position: relative;
}

.overflow-x-auto:has(.dropdown-open) {
  overflow: visible !important;
}

/* Custom scrollbar for dropdown */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
  border: 1px solid #f3f4f6;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Smooth scrolling for dropdown */
.dropdown-scroll {
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

/* Enhanced dropdown scrollbar for webkit browsers */
.dropdown-scroll::-webkit-scrollbar,
.inner-dropdown-scroll::-webkit-scrollbar,
div[style*="scrollbarWidth"]::-webkit-scrollbar {
  width: 8px;
}

.dropdown-scroll::-webkit-scrollbar-track,
.inner-dropdown-scroll::-webkit-scrollbar-track,
div[style*="scrollbarWidth"]::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 4px;
}

.dropdown-scroll::-webkit-scrollbar-thumb,
.inner-dropdown-scroll::-webkit-scrollbar-thumb,
div[style*="scrollbarWidth"]::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 4px;
  border: 1px solid #f9fafb;
}

.dropdown-scroll::-webkit-scrollbar-thumb:hover,
.inner-dropdown-scroll::-webkit-scrollbar-thumb:hover,
div[style*="scrollbarWidth"]::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Specific styles for inner dropdown scroll */
.inner-dropdown-scroll {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f9fafb;
}

.inner-dropdown-scroll::-webkit-scrollbar {
  width: 10px;
}

.inner-dropdown-scroll::-webkit-scrollbar-track {
  background: #f9fafb;
  border-radius: 6px;
  margin: 4px 0;
}

.inner-dropdown-scroll::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 6px;
  border: 2px solid #f9fafb;
}

.inner-dropdown-scroll::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Ensure table containers don't clip dropdowns */
.table-container {
  overflow: visible !important;
}

.table-container .overflow-x-auto {
  overflow-y: visible !important;
}

/* Ensure dropdown portals are always on top */
div[style*="position: fixed"][style*="z-index"] {
  z-index: 9999 !important;
}

/* Auto-scroll behavior for status dropdowns */
.status-dropdown-container {
  scroll-margin-top: 100px;
  scroll-margin-bottom: 100px;
}
