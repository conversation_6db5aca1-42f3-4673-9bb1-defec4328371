# Database Configuration
# Replace with your actual database URL (Neon, Supabase, or other PostgreSQL provider)
DATABASE_URL="postgresql://username:password@host:port/database?schema=public"

# JWT Configuration
# Generate a secure random string for production
JWT_SECRET="your-jwt-secret-key-change-this-in-production"

# NextAuth Configuration
# Generate a secure random string for production
NEXTAUTH_SECRET="your-nextauth-secret-key-change-this-in-production"
NEXTAUTH_URL="https://your-app-name.vercel.app"

# Application Configuration
NODE_ENV="production"

# Optional: Skip environment validation during build (for Vercel)
SKIP_ENV_VALIDATION="true"
