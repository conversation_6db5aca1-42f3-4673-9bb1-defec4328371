{"name": "innoventory", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "node scripts/build.js", "build:vercel": "node scripts/build.js", "start": "next start", "lint": "next lint --fix", "lint:check": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts", "db:clear": "tsx scripts/clear-demo-data.ts", "db:studio": "prisma studio", "postinstall": "prisma generate || echo 'Prisma generation failed, continuing...'", "vercel-build": "node scripts/build.js", "fix-prisma": "prisma generate", "dev:clean": "npm run fix-prisma && npm run dev"}, "prisma": {"seed": "tsx prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@neondatabase/serverless": "^1.0.0", "@prisma/client": "^6.9.0", "@types/animejs": "^3.1.13", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.9", "@types/leaflet": "^1.9.18", "animejs": "^3.2.1", "bcryptjs": "^3.0.2", "chart.js": "^4.4.9", "framer-motion": "^12.16.0", "jsonwebtoken": "^9.0.2", "leaflet": "^1.9.4", "lucide-react": "^0.513.0", "next": "15.3.3", "next-auth": "^4.24.11", "prisma": "^6.9.0", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0", "vercel": "^42.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4", "tsx": "^4.19.4", "typescript": "^5"}}